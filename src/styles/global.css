@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'Hack';
  src: url(/assets/fonts/Hack-NF.ttf);
  display: swap;
}

* {
  font-family: 'Hack', monospace;
}

html,
body,
body > div:first-child,
div#__next,
div#__next > div {
  height: 100%;
  overflow: auto;
  scroll-behavior: smooth;
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #1e252e;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: #ebdbb2;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ff8037;
}

/* Ensure smooth scrolling for all scrollable elements */
.overflow-y-auto,
.overflow-auto {
  scroll-behavior: smooth;
}

/* Auto-scroll container styles */
.scroll-smooth {
  scroll-behavior: smooth;
}

/* Custom scrollbar for modal and blog popups */
.custom-scrollbar::-webkit-scrollbar {
  width: 10px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #D79921;
  border-radius: 5px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background: #3C3836;
  border-radius: 5px;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #D79921 #3C3836;
}

/* Blog title styling */
.blog-title {
  color: #83a598;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.blog-title:hover {
  color: #b8bb26;
}

/* Details and Summary styling for Notion dropdowns */
details {
  margin: 1rem 0;
  padding: 0.5rem;
  border: 1px solid #ebdbb2;
  border-radius: 4px;
  background: rgba(235, 219, 178, 0.1);
}

summary {
  cursor: pointer;
  font-weight: 600;
  color: #ebdbb2;
  padding: 0.25rem 0.5rem;
  border-radius: 2px;
  transition: background-color 0.2s ease;
  display: list-item;
  list-style-type: disclosure-closed;
}

summary::marker {
  color: #ebdbb2;
  font-size: 0.9em;
  margin-right: 0.5rem;
}

details[open] summary {
  list-style-type: disclosure-open;
}

/* Ensure summary content is properly aligned */
summary > * {
  display: inline;
  vertical-align: middle;
}

/* Style links within summary */
summary a {
  color: #83a598;
  text-decoration: underline;
  transition: color 0.2s ease;
}

summary a:hover {
  color: #b8bb26;
}

summary:hover {
  background: rgba(235, 219, 178, 0.2);
}

details[open] summary {
  border-bottom: 1px solid #ebdbb2;
  margin-bottom: 0.5rem;
  list-style-type: disclosure-open;
}

details > *:not(summary) {
  padding: 0.5rem;
}
