{"name": "liveterm", "version": "0.1.0", "license": "MIT", "author": {"name": "<PERSON>", "url": "https://cveinnt.com", "email": "<EMAIL>"}, "scripts": {"dev": "node scripts/prepare-sql.js && next dev", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky install", "postinstall": "node scripts/prepare-sql.js"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@tailwindcss/typography": "^0.5.16", "axios": "^1.10.0", "marked": "^16.1.1", "next": "^15.4.2", "react": "18.1.0", "react-dom": "18.1.0", "react-icons": "^4.3.1", "react-markdown": "^8.0.7", "rehype-raw": "^7.0.0", "remark-html": "^16.0.1", "sql.js": "^1.8.0"}, "devDependencies": {"@types/node": "17.0.32", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "autoprefixer": "^10.4.7", "eslint": "8.15.0", "eslint-config-next": "^12.1.6", "eslint-plugin-next": "^0.0.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.29.4", "husky": "^8.0.1", "postcss": "^8.4.13", "prettier": "^2.6.2", "tailwindcss": "^3.0.24", "typescript": "^5.0.0"}}