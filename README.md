# Adit's Web

Just another terminal website...

Notable features
- No data is stored server-side, everything is in an in-browser SQL DB using [`sql.js`](https://github.com/sql-js/sql.js?tab=readme-ov-file)
- Users can ask an AI agent about me, the agent uses a mix of tools to answer the question (SQL, RAG, ...more to come)
- Most of the interesting quirks of this website are actually in the [backend repo](https://github.com/adit-bala/knowledge-base)

# Running Locally

Clone the repo
```
<NAME_EMAIL>:adit-bala/portfolio.git && cd portfolio
```

```
yarn install & yarn dev # need to have yarn installed
```

# Configuration

Please refer to the original repo for instructions on configuration (located at the bottom), but tldr, is look at `config.json`

credit for the original repo: [LiveTerm](https://github.com/Cveinnt/LiveTerm)
